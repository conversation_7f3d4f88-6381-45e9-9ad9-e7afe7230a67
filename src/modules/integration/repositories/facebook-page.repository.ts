import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { FacebookPage } from '@modules/integration/entities';
import { AppException, ErrorCode } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { PaginatedResult } from '@common/response';

@Injectable()
export class FacebookPageRepository extends Repository<FacebookPage> {
  private readonly logger = new Logger(FacebookPageRepository.name);

  constructor(private dataSource: DataSource) {
    super(FacebookPage, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho FacebookPage
   * @returns SelectQueryBuilder cho FacebookPage
   */
  createBaseQuery(): SelectQueryBuilder<FacebookPage> {
    return this.createQueryBuilder('page');
  }

  /**
   * Tìm Facebook page theo ID
   * @param pageId ID của Facebook page
   * @returns Facebook page nếu tìm thấy, null nếu không tìm thấy
   */
  async findByPageId(pageId: string): Promise<FacebookPage | null> {
    try {
      return this.createBaseQuery()
        .where('page.facebook_page_id = :pageId', { pageId })
        .select([
          'page.id',
          'page.facebookPageId',
          'page.pageName',
          'page.avatarPage',
          'page.agentId'
        ])
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm Facebook page theo ID: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND);
    }
  }

  /**
   * Tìm Facebook page theo ID và agent ID
   * @param pageId ID của Facebook page
   * @param agentId ID của agent
   * @returns Facebook page nếu tìm thấy, null nếu không tìm thấy
   */
  async findByPageIdAndAgentId(pageId: string, agentId: string): Promise<FacebookPage | null> {
    try {
      return this.createBaseQuery()
        .where('page.facebook_page_id = :pageId', { pageId })
        .andWhere('page.agent_id = :agentId', { agentId })
        .select([
          'page.id',
          'page.facebookPageId',
          'page.pageName',
          'page.avatarPage',
          'page.agentId'
        ])
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm Facebook page theo ID và agent ID: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND);
    }
  }

  /**
   * Tìm tất cả Facebook page theo agent ID
   * @param agentId ID của agent
   * @returns Danh sách Facebook page
   */
  async findAllByAgentId(agentId: string): Promise<FacebookPage[]> {
    try {
      return this.createBaseQuery()
        .where('page.agent_id = :agentId', { agentId })
        .select([
          'page.id',
          'page.facebookPageId',
          'page.pageName',
          'page.avatarPage'
        ])
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm tất cả Facebook page theo agent ID: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND);
    }
  }

  /**
   * Tìm tất cả Facebook page theo Facebook page ID
   * @param facebookPageId ID của Facebook page
   * @returns Danh sách Facebook page
   */
  async findAllByFacebookPageId(facebookPageId: string): Promise<FacebookPage[]> {
    try {
      return this.createBaseQuery()
        .where('page.facebook_page_id = :facebookPageId', { facebookPageId })
        .select([
          'page.id',
          'page.facebookPageId',
          'page.facebookPersonalId',
          'page.pageName',
          'page.avatarPage',
          'page.isError',
          'page.isActive',
          'page.agentId'
        ])
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm tất cả Facebook page theo Facebook page ID: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Tìm Facebook page theo Facebook page ID và Facebook personal ID
   * @param facebookPageId ID của Facebook page
   * @param facebookPersonalId ID của Facebook personal
   * @returns Facebook page nếu tìm thấy, null nếu không tìm thấy
   */
  async findByFacebookPageIdAndPersonalId(facebookPageId: string, facebookPersonalId: string): Promise<FacebookPage | null> {
    try {
      return this.createBaseQuery()
        .where('page.facebook_page_id = :facebookPageId', { facebookPageId })
        .andWhere('page.facebook_personal_id = :facebookPersonalId', { facebookPersonalId })
        .select([
          'page.id',
          'page.facebookPageId',
          'page.facebookPersonalId',
          'page.pageName',
          'page.pageAccessToken',
          'page.avatarPage',
          'page.isError',
          'page.isActive',
          'page.agentId'
        ])
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm Facebook page theo Facebook page ID và personal ID: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Đánh dấu tất cả các Facebook page có cùng Facebook page ID nhưng khác Facebook personal ID là lỗi
   * @param facebookPageId ID của Facebook page
   * @param excludeFacebookPersonalId ID của Facebook personal cần loại trừ
   * @returns Số lượng bản ghi đã cập nhật
   */
  async markOtherPagesAsError(facebookPageId: string, excludeFacebookPersonalId: string): Promise<number> {
    try {
      const result = await this.createQueryBuilder()
        .update(FacebookPage)
        .set({ isError: true })
        .where('facebook_page_id = :facebookPageId', { facebookPageId })
        .andWhere('facebook_personal_id != :excludeFacebookPersonalId', { excludeFacebookPersonalId })
        .execute();

      return result.affected || 0;
    } catch (error) {
      this.logger.error(`Lỗi khi đánh dấu Facebook page là lỗi: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * Tìm tất cả Facebook page theo danh sách Facebook page ID
   * @param facebookPageIds Danh sách ID của Facebook page
   * @param includeDeleted Có bao gồm các trang đã xóa mềm không (mặc định là false)
   * @returns Danh sách Facebook page
   */
  async findAllByFacebookPageIds(facebookPageIds: string[], includeDeleted: boolean = false): Promise<FacebookPage[]> {
    try {
      if (!facebookPageIds || facebookPageIds.length === 0) {
        return [];
      }

      const query = this.createBaseQuery()
        .where('page.facebook_page_id IN (:...facebookPageIds)', { facebookPageIds })
        .select([
          'page.id',
          'page.facebookPageId',
          'page.facebookPersonalId',
          'page.pageName',
          'page.pageAccessToken',
          'page.avatarPage',
          'page.isError',
          'page.isActive',
          'page.agentId',
          'page.deletedAt'
        ]);

      // Nếu không bao gồm các trang đã xóa mềm, thêm điều kiện
      if (!includeDeleted) {
        query.andWhere('page.deleted_at IS NULL');
      }

      return query.getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm tất cả Facebook page theo danh sách Facebook page ID: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Lấy danh sách trang Facebook của người dùng với phân trang và sắp xếp
   * @param userId ID của người dùng
   * @param options Các tùy chọn truy vấn (phân trang, sắp xếp)
   * @returns Danh sách trang Facebook và thông tin agent kèm tổng số bản ghi
   */
  async findPagesByUserId(
    userId: number,
    options: {
      page?: number;
      limit?: number;
      sortBy?: string;
      search?: string;
      sortDirection?: string;
      includeDeleted?: boolean;
      isConnectAgent?: boolean;
    } = {}
  ): Promise<{ items: any[]; total: number; page: number; limit: number }> {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'created_at',
        includeDeleted = false
      } = options;

      // Luôn sắp xếp theo DESC (mới nhất trước)
      const sortOrder = 'DESC';

      // Tính toán offset
      const offset = (page - 1) * limit;

      // Xây dựng câu truy vấn cơ bản
      let queryParams: any[] = [userId];
      let paramIndex = 2; // Bắt đầu từ $2 vì $1 đã được sử dụng cho userId

      let countQuery = `
        SELECT COUNT(*) as total
        FROM facebook_page fp
        INNER JOIN facebook_personal fpers ON fp.facebook_personal_id = fpers.id
        WHERE fpers.user_id = $1
      `;

      let dataQuery = `
        SELECT
          fp.id,
          fp.facebook_page_id,
          fp.facebook_personal_id,
          fp.page_name,
          fp.avatar_page,
          fp.is_active,
          fp.agent_id,
          fp.is_error,
          fp.created_at,
          a.id as agent_id,
          a.name as agent_name,
          a.avatar as agent_avatar,
          fpers.name as facebook_personal_name
        FROM facebook_page fp
        LEFT JOIN agents a ON fp.agent_id = a.id
        INNER JOIN facebook_personal fpers ON fp.facebook_personal_id = fpers.id
        WHERE fpers.user_id = $1
      `;

      // Nếu không bao gồm các trang đã xóa mềm, thêm điều kiện
      if (!includeDeleted) {
        countQuery += ` AND fp.deleted_at IS NULL`;
        dataQuery += ` AND fp.deleted_at IS NULL`;
      }

      if (options.isConnectAgent !== undefined) {
        countQuery += ` AND fp.agent_id IS ${options.isConnectAgent ? 'NOT' : ''} NULL`;
        dataQuery += ` AND fp.agent_id IS ${options.isConnectAgent ? 'NOT' : ''} NULL`;
      }

      if (options.search) {
        countQuery += ` AND (fp.page_name ILIKE $${paramIndex} OR fpers.name ILIKE $${paramIndex})`;
        dataQuery += ` AND (fp.page_name ILIKE $${paramIndex} OR fpers.name ILIKE $${paramIndex})`;
        queryParams.push(`%${options.search}%`, `%${options.search}%`);
        paramIndex += 2;
      }

      // Thêm sắp xếp
      let orderByClause = '';
      switch (sortBy) {
        case 'page_name':
          orderByClause = 'fp.page_name';
          break;
        case 'personal_name':
          orderByClause = 'fpers.name';
          break;
        case 'created_at':
        default:
          orderByClause = 'fp.created_at';
          break;
      }

      dataQuery += ` ORDER BY ${orderByClause} ${sortOrder}`;


      // Thêm phân trang
      dataQuery += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      queryParams.push(limit, offset);

      // Thực hiện truy vấn đếm tổng số bản ghi
      const countParams = queryParams.slice(0, paramIndex - 1);
      const countResult = await this.dataSource.query(countQuery, countParams);
      const total = parseInt(countResult[0].total, 10);

      // Thực hiện truy vấn lấy dữ liệu
      const items = await this.dataSource.query(dataQuery, queryParams);

      return {
        items: items || [],
        total,
        page,
        limit
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách trang Facebook theo user ID: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách trang Facebook'
      );
    }
  }

  /**
   * Tìm các trang Facebook của người dùng theo danh sách ID
   * @param userId ID của người dùng
   * @param pageIds Danh sách ID của các trang Facebook
   * @returns Danh sách trang Facebook
   */
  async findPagesByUserIdAndPageIds(userId: number, pageIds: string[]): Promise<FacebookPage[]> {
    try {
      if (!pageIds || pageIds.length === 0) {
        return [];
      }

      return this.createQueryBuilder('page')
        .innerJoin('facebook_personal', 'personal', 'page.facebook_personal_id = personal.id')
        .where('personal.user_id = :userId', { userId })
        .andWhere('page.id IN (:...pageIds)', { pageIds })
        .andWhere('page.deleted_at IS NULL') // Chỉ lấy trang chưa bị xóa mềm
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm trang Facebook theo user ID và page IDs: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Đánh dấu xóa mềm nhiều trang Facebook
   * @param pages Danh sách trang Facebook cần xóa mềm
   * @returns Số lượng trang đã xóa mềm
   */
  async softDeleteMany(pages: FacebookPage[]): Promise<number> {
    try {
      if (!pages || pages.length === 0) {
        return 0;
      }

      const currentTime = Date.now();

      // Cập nhật trường deletedAt và isActive cho tất cả các trang
      for (const page of pages) {
        page.deletedAt = currentTime;
        page.isActive = false;
      }

      // Lưu các thay đổi
      const savedPages = await this.save(pages);
      return savedPages.length;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa mềm nhiều trang Facebook: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * Tìm các trang Facebook của tài khoản Facebook cá nhân chưa bị xóa mềm
   * @param personalId ID của tài khoản Facebook cá nhân
   * @returns Danh sách trang Facebook
   */
  async findActivePagesByPersonalId(personalId: string): Promise<FacebookPage[]> {
    try {
      return this.createQueryBuilder('page')
        .where('page.facebook_personal_id = :personalId', { personalId })
        .andWhere('page.deleted_at IS NULL') // Chỉ lấy các trang chưa bị xóa mềm
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm trang Facebook theo personal ID: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Tìm trang Facebook của người dùng theo ID trang
   * @param userId ID của người dùng
   * @param pageId ID của trang Facebook
   * @returns Trang Facebook nếu tìm thấy, null nếu không tìm thấy
   */
  async findPageByUserIdAndPageId(userId: number, pageId: string): Promise<FacebookPage | null> {
    try {
      return this.createQueryBuilder('page')
        .innerJoin('facebook_personal', 'personal', 'page.facebook_personal_id = personal.id')
        .where('personal.user_id = :userId', { userId })
        .andWhere('page.id = :pageId', { pageId })
        .andWhere('page.deleted_at IS NULL') // Chỉ lấy trang chưa bị xóa mềm
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm trang Facebook theo user ID và page ID: ${error.message}`, error.stack);
      return null;
    }
  }

    /**
   * Tìm trang Facebook của người dùng theo ID trang
   * @param userId ID của người dùng
   * @param pageId ID của trang Facebook
   * @returns Trang Facebook nếu tìm thấy, null nếu không tìm thấy
   */
  async findPageByUserIdAndPageIds(userId: number, pageId: string[]): Promise<FacebookPage | null> {
    try {
      return this.createQueryBuilder('page')
        .innerJoin('facebook_personal', 'personal', 'page.facebook_personal_id = personal.id')
        .where('personal.user_id = :userId', { userId })
        .andWhere('page.id IN (:...pageId)', { pageId })
        .andWhere('page.deleted_at IS NULL') // Chỉ lấy trang chưa bị xóa mềm
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm trang Facebook theo user ID và page ID: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tìm trang Facebook theo Facebook Page ID và User ID
   * @param facebookPageId ID của Facebook Page
   * @param userId ID của người dùng
   * @returns Trang Facebook nếu tìm thấy, null nếu không tìm thấy
   */
  async findByFacebookPageIdAndUserId(facebookPageId: string, userId: number): Promise<FacebookPage | null> {
    try {
      return this.createQueryBuilder('page')
        .innerJoin('facebook_personal', 'personal', 'page.facebook_personal_id = personal.id')
        .where('page.facebook_page_id = :facebookPageId', { facebookPageId })
        .andWhere('personal.user_id = :userId', { userId })
        .andWhere('page.deleted_at IS NULL')
        .select([
          'page.id',
          'page.facebookPageId',
          'page.pageName',
          'page.avatarPage',
          'page.isActive',
          'page.agentId',
          'page.pageAccessToken'
        ])
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm trang Facebook theo Facebook Page ID và User ID: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Lấy danh sách Facebook Page đã tích hợp với Agent
   * @param agentId ID của Agent
   * @returns Danh sách Facebook Page
   */
  async findByAgentId(agentId: string): Promise<FacebookPage[]> {
    try {
      return this.createBaseQuery()
        .where('page.agent_id = :agentId', { agentId })
        .andWhere('page.deleted_at IS NULL')
        .select([
          'page.id',
          'page.facebookPageId',
          'page.pageName',
          'page.avatarPage',
          'page.isActive'
        ])
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách Facebook Page theo Agent ID: ${error.message}`, error.stack);
      throw error; // Throw error thay vì return []
    }
  }

  /**
   * Lấy danh sách Facebook Page theo agent ID với phân trang
   * @param agentId ID của agent
   * @param page Trang hiện tại
   * @param limit Số lượng item trên mỗi trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param isActive Lọc theo trạng thái hoạt động (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Kết quả phân trang với thông tin Facebook Page
   */
  async findByAgentIdWithPagination(
    agentId: string,
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'createdAt',
    sortDirection: string = 'DESC',
  ): Promise<PaginatedResult<FacebookPage>> {
    try {

      // Tạo base query chỉ với điều kiện agent (không có search) để tính hasItems
      const baseQueryWithoutSearch = this.createBaseQuery()
        .where('page.agent_id = :agentId', { agentId })
        .andWhere('page.deleted_at IS NULL');

      // Đếm tổng số bản ghi của agent (không filter search) để tính hasItems
      const totalAgentItems = await baseQueryWithoutSearch.clone().getCount();
      const hasItems = totalAgentItems > 0;

      // Tạo base query với điều kiện chung (bao gồm search nếu có)
      const baseQuery = baseQueryWithoutSearch.clone();

      // Thêm điều kiện tìm kiếm nếu có
      if (search && search.trim()) {
        baseQuery.andWhere(
          'LOWER(page.page_name) LIKE LOWER(:search)',
          { search: `%${search.trim()}%` }
        );
      }

      // Đếm tổng số bản ghi sau khi filter
      const countQuery = baseQuery.clone();
      const totalItems = await countQuery.getCount();

      // Lấy dữ liệu với phân trang và sắp xếp
      const dataQuery = baseQuery.clone()
        .select([
          'page.id',
          'page.facebookPageId',
          'page.pageName',
          'page.avatarPage',
          'page.isActive',
          'page.createdAt'
        ]);

      // Áp dụng sắp xếp
      const sortField = this.getFacebookPageSortField(sortBy);
      dataQuery.orderBy(sortField, sortDirection.toUpperCase() as 'ASC' | 'DESC');

      const items = await dataQuery
        .skip((page - 1) * limit)
        .take(limit)
        .getMany();

      // Tính toán metadata phân trang
      const totalPages = Math.ceil(totalItems / limit);
      const itemCount = items.length;

      return {
        items,
        meta: {
          totalItems,
          itemCount,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
          hasItems,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách Facebook Page theo agent ID với phân trang: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật trạng thái active của Facebook Page
   * @param pageId ID của Facebook Page
   * @param isActive Trạng thái active mới
   */
  async updateActiveStatus(pageId: string, isActive: boolean): Promise<void> {
    try {
      await this.createQueryBuilder()
        .update(FacebookPage)
        .set({ isActive })
        .where('id = :pageId', { pageId })
        .andWhere('deleted_at IS NULL')
        .execute();
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật trạng thái active của Facebook Page: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật agent ID cho Facebook Page
   * @param pageId ID của Facebook Page
   * @param agentId ID của agent (null để gỡ kết nối)
   */
  async updateAgentId(pageId: string, agentId: string | null): Promise<void> {
    try {
      await this.createQueryBuilder()
        .update(FacebookPage)
        .set({ agentId })
        .where('id = :pageId', { pageId })
        .andWhere('deleted_at IS NULL')
        .execute();

      this.logger.log(`Đã cập nhật agent ID cho Facebook Page ${pageId}: ${agentId || 'null'}`);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật agent ID cho Facebook Page: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Kiểm tra Facebook Page đã được kết nối với agent khác chưa
   * @param pageId ID của Facebook Page
   * @param excludeAgentId ID của agent cần loại trừ (không kiểm tra)
   * @returns Agent ID hiện tại nếu đã kết nối, null nếu chưa kết nối
   */
  async findConnectedAgentId(pageId: string, excludeAgentId?: string): Promise<string | null> {
    try {
      const query = this.createBaseQuery()
        .where('page.id = :pageId', { pageId })
        .andWhere('page.agent_id IS NOT NULL')
        .select(['page.agentId']);

      if (excludeAgentId) {
        query.andWhere('page.agent_id != :excludeAgentId', { excludeAgentId });
      }

      const result = await query.getOne();
      return result?.agentId || null;
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra kết nối agent cho Facebook Page: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Gỡ kết nối tất cả Facebook Page khỏi agent
   * @param agentId ID của agent
   */
  async disconnectAllPagesFromAgent(agentId: string): Promise<void> {
    try {
      await this.createQueryBuilder()
        .update(FacebookPage)
        .set({ agentId: null })
        .where('agent_id = :agentId', { agentId })
        .andWhere('deleted_at IS NULL')
        .execute();

      this.logger.log(`Đã gỡ kết nối tất cả Facebook Page khỏi agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ kết nối Facebook Page khỏi agent: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Kiểm tra danh sách Facebook Pages có thuộc về user không
   * @param userId ID của user
   * @param pageIds Danh sách ID của Facebook Pages
   * @returns Danh sách page IDs không thuộc về user
   */
  async findPagesNotOwnedByUser(userId: number, pageIds: string[]): Promise<string[]> {
    try {
      if (!pageIds || pageIds.length === 0) {
        return [];
      }

      const ownedPages = await this.createQueryBuilder('page')
        .innerJoin('facebook_personal', 'personal', 'page.facebook_personal_id = personal.id')
        .where('personal.user_id = :userId', { userId })
        .andWhere('page.id IN (:...pageIds)', { pageIds })
        .andWhere('page.deleted_at IS NULL')
        .select(['page.id'])
        .getMany();

      const ownedPageIds = ownedPages.map(page => page.id);
      const notOwnedPageIds = pageIds.filter(pageId => !ownedPageIds.includes(pageId));

      return notOwnedPageIds;
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra ownership của Facebook Pages: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Kiểm tra danh sách Facebook Pages đã được kết nối với agents khác chưa
   * @param pageIds Danh sách ID của Facebook Pages
   * @param excludeAgentId ID của agent cần loại trừ
   * @returns Danh sách {pageId, agentId} đã được kết nối
   */
  async findPagesConnectedToOtherAgents(
    pageIds: string[],
    excludeAgentId?: string
  ): Promise<Array<{pageId: string, agentId: string}>> {
    try {
      if (!pageIds || pageIds.length === 0) {
        return [];
      }

      const query = this.createBaseQuery()
        .where('page.id IN (:...pageIds)', { pageIds })
        .andWhere('page.agent_id IS NOT NULL')
        .select(['page.id', 'page.agentId']);

      if (excludeAgentId) {
        query.andWhere('page.agent_id != :excludeAgentId', { excludeAgentId });
      }

      const connectedPages = await query.getMany();

      return connectedPages.map(page => ({
        pageId: page.id,
        agentId: page.agentId!
      }));
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra kết nối của Facebook Pages: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật agent ID cho nhiều Facebook Pages cùng lúc
   * @param pageIds Danh sách ID của Facebook Pages
   * @param agentId ID của agent (null để gỡ kết nối)
   */
  async updateAgentIdForMultiplePages(pageIds: string[], agentId: string | null): Promise<void> {
    try {
      if (!pageIds || pageIds.length === 0) {
        return;
      }

      await this.createQueryBuilder()
        .update(FacebookPage)
        .set({ agentId })
        .where('id IN (:...pageIds)', { pageIds })
        .andWhere('deleted_at IS NULL')
        .execute();

      this.logger.log(
        `Đã cập nhật agent ID cho ${pageIds.length} Facebook Pages: ${agentId || 'null'}`
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật agent ID cho nhiều Facebook Pages: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Tìm các trang Facebook không còn tồn tại trong API
   * @param facebookPersonalId ID của tài khoản Facebook cá nhân
   * @param existingPageIds Danh sách ID của các trang Facebook còn tồn tại trong API
   * @returns Danh sách trang Facebook không còn tồn tại
   */
  async findRemovedPages(facebookPersonalId: string, existingPageIds: string[]): Promise<FacebookPage[]> {
    try {
      const query = this.createQueryBuilder('page')
        .where('page.facebook_personal_id = :facebookPersonalId', { facebookPersonalId })
        .andWhere('page.deleted_at IS NULL'); // Chỉ lấy các trang chưa bị xóa mềm

      if (existingPageIds.length > 0) {
        query.andWhere('page.facebook_page_id NOT IN (:...existingPageIds)', { existingPageIds });
      }

      return query.getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm các trang Facebook không còn tồn tại: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Mapping sort field cho Facebook Page
   * @param sortBy Trường sắp xếp từ DTO
   * @returns Tên trường trong database
   */
  private getFacebookPageSortField(sortBy: string): string {
    const sortFieldMap: Record<string, string> = {
      pageName: 'page.page_name',
      createdAt: 'page.created_at',
      isActive: 'page.is_active',
    };

    return sortFieldMap[sortBy] || 'page.created_at';
  }
}
