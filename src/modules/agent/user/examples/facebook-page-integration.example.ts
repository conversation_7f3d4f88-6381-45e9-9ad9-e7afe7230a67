/**
 * Example: Facebook Page Integration với Agent
 * 
 * File này minh họa cách sử dụng logic tích hợp Facebook Page với Agent
 */

import { CreateAgentDto } from '../dto/agent/create-agent.dto';

// ===== EXAMPLE 1: Tạo Agent với Facebook Pages =====

/**
 * DTO để tạo agent với Facebook Page integration
 */
const createAgentWithFacebookPages: CreateAgentDto = {
  name: 'Customer Support Agent',
  typeId: 1, // Type agent hỗ trợ enableOutputToMessenger
  instruction: 'Bạn là trợ lý chăm sóc khách hàng chuyên nghiệp',
  modelConfig: {
    temperature: 0.7,
    maxTokens: 1000,
  },
  
  // Facebook Page Integration
  outputMessenger: {
    facebookPageIds: [
      'page-uuid-1', // Facebook Page 1
      'page-uuid-2', // Facebook Page 2
      'page-uuid-3', // Facebook Page 3
    ]
  },

  // <PERSON><PERSON><PERSON> fields khác
  systemModelId: 'model-uuid-1',
  keyLlmId: 'key-uuid-1',
  avatarMimeType: 'image/jpeg',
};

// ===== EXAMPLE 2: Xử lý các trường hợp khác nhau =====

/**
 * Trường hợp 1: Agent chỉ có 1 Facebook Page
 */
const singlePageAgent: CreateAgentDto = {
  name: 'Single Page Agent',
  typeId: 1,
  instruction: 'Agent chỉ quản lý 1 trang Facebook',
  modelConfig: { temperature: 0.5 },
  
  outputMessenger: {
    facebookPageIds: ['page-uuid-only-one']
  }
};

/**
 * Trường hợp 2: Agent không có Facebook Page
 */
const noPageAgent: CreateAgentDto = {
  name: 'No Page Agent',
  typeId: 1,
  instruction: 'Agent không kết nối với Facebook Page nào',
  modelConfig: { temperature: 0.5 },
  
  // Không có outputMessenger hoặc facebookPageIds rỗng
  outputMessenger: {
    facebookPageIds: []
  }
};

/**
 * Trường hợp 3: Agent với nhiều output channels
 */
const multiChannelAgent: CreateAgentDto = {
  name: 'Multi Channel Agent',
  typeId: 1,
  instruction: 'Agent hỗ trợ nhiều kênh output',
  modelConfig: { temperature: 0.7 },
  
  // Facebook Messenger
  outputMessenger: {
    facebookPageIds: ['page-uuid-1', 'page-uuid-2']
  },
  
  // Website Live Chat
  outputWebsite: {
    userWebsiteIds: ['website-uuid-1', 'website-uuid-2']
  },
  
  // Zalo OA
  outputZalo: {
    zaloOfficialAccountIds: ['zalo-uuid-1']
  }
};

// ===== EXAMPLE 3: Error Handling =====

/**
 * Các lỗi có thể xảy ra và cách xử lý
 */
const errorExamples = {
  // Lỗi: Facebook Page không thuộc về user
  FACEBOOK_PAGE_NOT_OWNED: {
    code: 40152,
    message: 'Facebook Page không thuộc về người dùng này',
    scenario: 'User cố gắng kết nối page của người khác'
  },
  
  // Lỗi: Facebook Page đã kết nối với agent khác
  FACEBOOK_PAGE_ALREADY_CONNECTED: {
    code: 40150,
    message: 'Facebook Page đã được kết nối với agent khác',
    scenario: 'Page đã được sử dụng bởi agent khác (sẽ tự động gỡ kết nối cũ)'
  },
  
  // Lỗi: Không thể kết nối
  FACEBOOK_PAGE_CONNECTION_FAILED: {
    code: 40151,
    message: 'Không thể kết nối Facebook Page với agent',
    scenario: 'Lỗi database hoặc network'
  }
};

// ===== EXAMPLE 4: API Usage =====

/**
 * Cách gọi API để tạo agent với Facebook Pages
 */
const apiUsageExample = {
  endpoint: 'POST /v1/user/agents',
  headers: {
    'Authorization': 'Bearer <user-jwt-token>',
    'Content-Type': 'application/json'
  },
  body: createAgentWithFacebookPages,
  
  // Response thành công
  successResponse: {
    statusCode: 201,
    data: {
      id: 'agent-uuid-created',
      avatarUploadUrl: 'https://s3.amazonaws.com/presigned-url'
    }
  },
  
  // Response lỗi
  errorResponse: {
    statusCode: 403,
    error: {
      code: 40152,
      message: 'Facebook Page không thuộc về người dùng này'
    }
  }
};

// ===== EXAMPLE 5: Database State Changes =====

/**
 * Minh họa thay đổi trong database khi tích hợp
 */
const databaseStateExample = {
  // Trước khi tạo agent
  beforeCreation: {
    facebook_page: [
      {
        id: 'page-uuid-1',
        facebook_page_id: 'fb-123456789',
        page_name: 'My Business Page',
        agent_id: null, // Chưa kết nối với agent nào
        deleted_at: null
      },
      {
        id: 'page-uuid-2',
        facebook_page_id: 'fb-987654321',
        page_name: 'Another Page',
        agent_id: 'old-agent-uuid', // Đã kết nối với agent khác
        deleted_at: null
      }
    ]
  },
  
  // Sau khi tạo agent với pages ['page-uuid-1', 'page-uuid-2']
  afterCreation: {
    facebook_page: [
      {
        id: 'page-uuid-1',
        facebook_page_id: 'fb-123456789',
        page_name: 'My Business Page',
        agent_id: 'new-agent-uuid', // Đã kết nối với agent mới
        deleted_at: null
      },
      {
        id: 'page-uuid-2',
        facebook_page_id: 'fb-987654321',
        page_name: 'Another Page',
        agent_id: 'new-agent-uuid', // Gỡ kết nối cũ, kết nối mới
        deleted_at: null
      }
    ]
  }
};

// ===== EXAMPLE 6: Testing Scenarios =====

/**
 * Các test cases để kiểm tra logic
 */
const testScenarios = [
  {
    name: 'Successful connection',
    setup: 'User owns Facebook pages, pages not connected to any agent',
    input: ['page-1', 'page-2'],
    expected: 'All pages connected to new agent'
  },
  {
    name: 'Disconnect from previous agent',
    setup: 'User owns Facebook pages, pages already connected to other agents',
    input: ['page-1', 'page-2'],
    expected: 'Pages disconnected from old agents, connected to new agent'
  },
  {
    name: 'Mixed ownership',
    setup: 'Some pages owned by user, some not',
    input: ['owned-page', 'not-owned-page'],
    expected: 'Error thrown for not-owned-page, owned-page not processed'
  },
  {
    name: 'Empty page list',
    setup: 'No Facebook pages provided',
    input: [],
    expected: 'No operations performed, no errors'
  }
];

export {
  createAgentWithFacebookPages,
  singlePageAgent,
  noPageAgent,
  multiChannelAgent,
  errorExamples,
  apiUsageExample,
  databaseStateExample,
  testScenarios
};
