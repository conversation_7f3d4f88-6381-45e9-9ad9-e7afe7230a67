# Facebook Page Integration với Agent

## Tổng quan

Tài liệu này mô tả logic tích hợp Facebook Page với Agent trong hệ thống, đả<PERSON> bảo mỗi Facebook Page chỉ có thể kết nối với một Agent duy nhất tại một thời điểm.

## Kiến trúc

### Database Schema

```sql
-- Bảng facebook_page đã có sẵn trường agent_id
CREATE TABLE facebook_page (
    id UUID PRIMARY KEY,
    facebook_page_id VARCHAR(255) NOT NULL,
    facebook_personal_id UUID NOT NULL,
    page_access_token VARCHAR(1000),
    page_name TEXT,
    is_active BOOLEAN,
    avatar_page VARCHAR(255),
    is_error BOOLEAN,
    agent_id UUID NULL, -- Kết nối với agent
    deleted_at BIGINT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Ràng buộc

1. **Một Facebook Page chỉ kết nối với một Agent**: `agent_id` trong bảng `facebook_page` chỉ có thể chứa một giá trị tại một thời điểm.
2. **Facebook Page phải thuộc về User**: Kiểm tra qua bảng `facebook_personal` và `user_id`.
3. **Tự động gỡ kết nối cũ**: Khi kết nối Facebook Page với Agent mới, tự động gỡ kết nối với Agent cũ.

## Implementation

### 1. Repository Methods

#### FacebookPageRepository

```typescript
/**
 * Cập nhật agent ID cho Facebook Page
 */
async updateAgentId(pageId: string, agentId: string | null): Promise<void>

/**
 * Kiểm tra Facebook Page đã được kết nối với agent khác chưa
 */
async findConnectedAgentId(pageId: string, excludeAgentId?: string): Promise<string | null>

/**
 * Tìm Facebook Page theo User ID và Page ID
 */
async findPageByUserIdAndPageId(userId: number, pageId: string): Promise<FacebookPage | null>

/**
 * Gỡ kết nối tất cả Facebook Page khỏi agent
 */
async disconnectAllPagesFromAgent(agentId: string): Promise<void>
```

### 2. Service Logic

#### AgentUserService

```typescript
/**
 * Xử lý Output Messenger block - Lưu quan hệ với Facebook Pages
 */
private async processOutputMessengerBlock(
  agentId: string,
  userId: number,
  outputMessenger: any,
): Promise<void>

/**
 * Kết nối Facebook Page với Agent (mỗi page chỉ kết nối với 1 agent)
 */
private async connectFacebookPageToAgent(
  agentId: string,
  userId: number,
  pageId: string,
): Promise<void>
```

## Luồng xử lý

### 1. Tạo Agent với Facebook Pages

```mermaid
sequenceDiagram
    participant User
    participant AgentService
    participant FacebookPageRepo
    participant Database

    User->>AgentService: createAgent(dto)
    AgentService->>AgentService: processOutputMessengerBlock()
    
    loop Mỗi Facebook Page ID
        AgentService->>FacebookPageRepo: findPageByUserIdAndPageId()
        FacebookPageRepo->>Database: SELECT * FROM facebook_page...
        Database-->>FacebookPageRepo: FacebookPage | null
        
        alt Page không tồn tại hoặc không thuộc user
            FacebookPageRepo-->>AgentService: null
            AgentService->>AgentService: throw FACEBOOK_PAGE_NOT_OWNED
        else Page hợp lệ
            FacebookPageRepo-->>AgentService: FacebookPage
            
            AgentService->>FacebookPageRepo: findConnectedAgentId()
            FacebookPageRepo->>Database: SELECT agent_id FROM facebook_page...
            Database-->>FacebookPageRepo: agentId | null
            
            alt Đã kết nối với agent khác
                FacebookPageRepo-->>AgentService: previousAgentId
                AgentService->>FacebookPageRepo: updateAgentId(pageId, null)
                FacebookPageRepo->>Database: UPDATE facebook_page SET agent_id = NULL...
            end
            
            AgentService->>FacebookPageRepo: updateAgentId(pageId, agentId)
            FacebookPageRepo->>Database: UPDATE facebook_page SET agent_id = ?...
        end
    end
```

### 2. Validation Steps

1. **Kiểm tra quyền sở hữu**: Facebook Page phải thuộc về User thông qua `facebook_personal.user_id`
2. **Kiểm tra kết nối hiện tại**: Nếu Page đã kết nối với Agent khác, gỡ kết nối cũ
3. **Tạo kết nối mới**: Cập nhật `agent_id` trong bảng `facebook_page`

## Error Handling

### Error Codes

```typescript
// Trong AGENT_ERROR_CODES
FACEBOOK_PAGE_ALREADY_CONNECTED: 40150,
FACEBOOK_PAGE_CONNECTION_FAILED: 40151,
FACEBOOK_PAGE_NOT_OWNED: 40152,
FACEBOOK_PAGE_DISCONNECTION_FAILED: 40153,
```

### Error Scenarios

1. **Facebook Page không tồn tại**: `FACEBOOK_PAGE_NOT_OWNED`
2. **Facebook Page không thuộc về User**: `FACEBOOK_PAGE_NOT_OWNED`
3. **Lỗi khi cập nhật database**: `FACEBOOK_PAGE_CONNECTION_FAILED`
4. **Lỗi khi gỡ kết nối**: `FACEBOOK_PAGE_DISCONNECTION_FAILED`

## Testing

### Unit Tests

```typescript
describe('Facebook Page Integration', () => {
  it('should connect Facebook page to agent successfully')
  it('should throw error when page not owned by user')
  it('should disconnect from previous agent before connecting new')
  it('should handle multiple page IDs in output messenger')
})
```

### Test Cases

1. **Kết nối thành công**: Page thuộc về user và chưa kết nối với agent nào
2. **Gỡ kết nối cũ**: Page đã kết nối với agent khác
3. **Lỗi quyền sở hữu**: Page không thuộc về user
4. **Xử lý nhiều pages**: Mảng nhiều Facebook Page IDs

## Logging

### Log Levels

- **INFO**: Bắt đầu và kết thúc quá trình
- **DEBUG**: Chi tiết từng bước xử lý
- **WARN**: Page không tồn tại hoặc đã kết nối với agent khác
- **ERROR**: Lỗi trong quá trình xử lý

### Log Messages

```typescript
// Thành công
`Processing output messenger block for agent ${agentId}`
`Connecting Facebook page ${pageId} to agent ${agentId}`
`Successfully connected Facebook page ${pageId} to agent ${agentId}`

// Cảnh báo
`Facebook page ${pageId} not found or not owned by user ${userId}`
`Facebook page ${pageId} is already connected to agent ${connectedAgentId}`

// Lỗi
`Error connecting Facebook page ${pageId} to agent ${agentId}: ${error.message}`
```

## Performance Considerations

1. **Batch Operations**: Xử lý từng page một cách tuần tự để đảm bảo tính nhất quán
2. **Transaction**: Sử dụng `@Transactional()` để đảm bảo atomicity
3. **Error Recovery**: Tiếp tục xử lý các pages khác nếu một page gặp lỗi

## Security

1. **Authorization**: Kiểm tra quyền sở hữu Facebook Page qua `user_id`
2. **Validation**: Validate tất cả input parameters
3. **Audit Trail**: Log tất cả thao tác kết nối/gỡ kết nối

## Future Enhancements

1. **Bulk Operations**: API để kết nối/gỡ kết nối nhiều pages cùng lúc
2. **History Tracking**: Lưu lịch sử kết nối giữa pages và agents
3. **Webhook Integration**: Tự động cập nhật khi page bị xóa từ Facebook
4. **Permission Management**: Kiểm tra quyền của page access token
