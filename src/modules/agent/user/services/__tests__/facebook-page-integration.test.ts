import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { AgentUserService } from '../agent-user.service';
import { FacebookPageRepository } from '@modules/integration/repositories';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AppException } from '@common/exceptions';

describe('AgentUserService - Facebook Page Integration', () => {
  let service: AgentUserService;
  let facebookPageRepository: jest.Mocked<FacebookPageRepository>;

  const mockFacebookPageRepository = {
    findPageByUserIdAndPageId: jest.fn(),
    findConnectedAgentId: jest.fn(),
    updateAgentId: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: AgentUserService,
          useValue: {
            connectFacebookPageToAgent: jest.fn(),
            processOutputMessengerBlock: jest.fn(),
          },
        },
        {
          provide: FacebookPageRepository,
          useValue: mockFacebookPageRepository,
        },
      ],
    }).compile();

    service = module.get<AgentUserService>(AgentUserService);
    facebookPageRepository = module.get(FacebookPageRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('connectFacebookPageToAgent', () => {
    const agentId = 'agent-123';
    const userId = 1;
    const pageId = 'page-456';

    it('should successfully connect Facebook page to agent', async () => {
      // Arrange
      const mockFacebookPage = {
        id: pageId,
        facebookPageId: 'fb-page-123',
        pageName: 'Test Page',
        agentId: null,
      };

      facebookPageRepository.findPageByUserIdAndPageId.mockResolvedValue(mockFacebookPage);
      facebookPageRepository.findConnectedAgentId.mockResolvedValue(null);
      facebookPageRepository.updateAgentId.mockResolvedValue(undefined);

      // Act & Assert
      await expect(
        service.connectFacebookPageToAgent(agentId, userId, pageId)
      ).resolves.not.toThrow();

      expect(facebookPageRepository.findPageByUserIdAndPageId).toHaveBeenCalledWith(userId, pageId);
      expect(facebookPageRepository.findConnectedAgentId).toHaveBeenCalledWith(pageId, agentId);
      expect(facebookPageRepository.updateAgentId).toHaveBeenCalledWith(pageId, agentId);
    });

    it('should throw error when Facebook page not found or not owned by user', async () => {
      // Arrange
      facebookPageRepository.findPageByUserIdAndPageId.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.connectFacebookPageToAgent(agentId, userId, pageId)
      ).rejects.toThrow(new AppException(AGENT_ERROR_CODES.FACEBOOK_PAGE_NOT_OWNED));

      expect(facebookPageRepository.findPageByUserIdAndPageId).toHaveBeenCalledWith(userId, pageId);
      expect(facebookPageRepository.findConnectedAgentId).not.toHaveBeenCalled();
      expect(facebookPageRepository.updateAgentId).not.toHaveBeenCalled();
    });

    it('should disconnect from previous agent before connecting to new agent', async () => {
      // Arrange
      const mockFacebookPage = {
        id: pageId,
        facebookPageId: 'fb-page-123',
        pageName: 'Test Page',
        agentId: 'previous-agent-789',
      };
      const previousAgentId = 'previous-agent-789';

      facebookPageRepository.findPageByUserIdAndPageId.mockResolvedValue(mockFacebookPage);
      facebookPageRepository.findConnectedAgentId.mockResolvedValue(previousAgentId);
      facebookPageRepository.updateAgentId.mockResolvedValue(undefined);

      // Act
      await service.connectFacebookPageToAgent(agentId, userId, pageId);

      // Assert
      expect(facebookPageRepository.findPageByUserIdAndPageId).toHaveBeenCalledWith(userId, pageId);
      expect(facebookPageRepository.findConnectedAgentId).toHaveBeenCalledWith(pageId, agentId);
      expect(facebookPageRepository.updateAgentId).toHaveBeenCalledTimes(2);
      expect(facebookPageRepository.updateAgentId).toHaveBeenNthCalledWith(1, pageId, null); // Disconnect old
      expect(facebookPageRepository.updateAgentId).toHaveBeenNthCalledWith(2, pageId, agentId); // Connect new
    });
  });

  describe('processOutputMessengerBlock', () => {
    const agentId = 'agent-123';
    const userId = 1;

    it('should process multiple Facebook page IDs', async () => {
      // Arrange
      const outputMessenger = {
        facebookPageIds: ['page-1', 'page-2', 'page-3'],
      };

      const connectSpy = jest.spyOn(service, 'connectFacebookPageToAgent').mockResolvedValue(undefined);

      // Act
      await service.processOutputMessengerBlock(agentId, userId, outputMessenger);

      // Assert
      expect(connectSpy).toHaveBeenCalledTimes(3);
      expect(connectSpy).toHaveBeenNthCalledWith(1, agentId, userId, 'page-1');
      expect(connectSpy).toHaveBeenNthCalledWith(2, agentId, userId, 'page-2');
      expect(connectSpy).toHaveBeenNthCalledWith(3, agentId, userId, 'page-3');
    });

    it('should handle empty Facebook page IDs array', async () => {
      // Arrange
      const outputMessenger = {
        facebookPageIds: [],
      };

      const connectSpy = jest.spyOn(service, 'connectFacebookPageToAgent');

      // Act
      await service.processOutputMessengerBlock(agentId, userId, outputMessenger);

      // Assert
      expect(connectSpy).not.toHaveBeenCalled();
    });

    it('should handle missing facebookPageIds property', async () => {
      // Arrange
      const outputMessenger = {};

      const connectSpy = jest.spyOn(service, 'connectFacebookPageToAgent');

      // Act
      await service.processOutputMessengerBlock(agentId, userId, outputMessenger);

      // Assert
      expect(connectSpy).not.toHaveBeenCalled();
    });
  });
});
