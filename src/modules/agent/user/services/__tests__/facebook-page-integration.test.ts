import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { AgentUserService } from '../agent-user.service';
import { FacebookPageRepository } from '@modules/integration/repositories';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AppException } from '@common/exceptions';

describe('AgentUserService - Facebook Page Integration (New Logic)', () => {
  let service: AgentUserService;
  let facebookPageRepository: jest.Mocked<FacebookPageRepository>;

  const mockFacebookPageRepository = {
    findPagesNotOwnedByUser: jest.fn(),
    findPagesConnectedToOtherAgents: jest.fn(),
    updateAgentIdForMultiplePages: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: AgentUserService,
          useValue: {
            validateAndConnectFacebookPages: jest.fn(),
            validateFacebookPagesOwnership: jest.fn(),
            validateFacebookPagesConnection: jest.fn(),
            processOutputMessengerBlock: jest.fn(),
          },
        },
        {
          provide: FacebookPageRepository,
          useValue: mockFacebookPageRepository,
        },
      ],
    }).compile();

    service = module.get<AgentUserService>(AgentUserService);
    facebookPageRepository = module.get(FacebookPageRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateAndConnectFacebookPages', () => {
    const agentId = 'agent-123';
    const userId = 1;
    const pageIds = ['page-1', 'page-2', 'page-3'];

    it('should successfully validate and connect all Facebook pages', async () => {
      // Arrange
      facebookPageRepository.findPagesNotOwnedByUser.mockResolvedValue([]);
      facebookPageRepository.findPagesConnectedToOtherAgents.mockResolvedValue([]);
      facebookPageRepository.updateAgentIdForMultiplePages.mockResolvedValue(undefined);

      // Act & Assert
      await expect(
        service.validateAndConnectFacebookPages(agentId, userId, pageIds)
      ).resolves.not.toThrow();

      expect(facebookPageRepository.findPagesNotOwnedByUser).toHaveBeenCalledWith(userId, pageIds);
      expect(facebookPageRepository.findPagesConnectedToOtherAgents).toHaveBeenCalledWith(pageIds, agentId);
      expect(facebookPageRepository.updateAgentIdForMultiplePages).toHaveBeenCalledWith(pageIds, agentId);
    });

    it('should throw error when some pages are not owned by user', async () => {
      // Arrange
      const notOwnedPages = ['page-2', 'page-3'];
      facebookPageRepository.findPagesNotOwnedByUser.mockResolvedValue(notOwnedPages);

      // Act & Assert
      await expect(
        service.validateAndConnectFacebookPages(agentId, userId, pageIds)
      ).rejects.toThrow(new AppException(AGENT_ERROR_CODES.FACEBOOK_PAGE_NOT_OWNED));

      expect(facebookPageRepository.findPagesNotOwnedByUser).toHaveBeenCalledWith(userId, pageIds);
      expect(facebookPageRepository.findPagesConnectedToOtherAgents).not.toHaveBeenCalled();
      expect(facebookPageRepository.updateAgentIdForMultiplePages).not.toHaveBeenCalled();
    });

    it('should throw error when some pages are already connected to other agents', async () => {
      // Arrange
      const connectedPages = [
        { pageId: 'page-1', agentId: 'other-agent-1' },
        { pageId: 'page-3', agentId: 'other-agent-2' }
      ];
      facebookPageRepository.findPagesNotOwnedByUser.mockResolvedValue([]);
      facebookPageRepository.findPagesConnectedToOtherAgents.mockResolvedValue(connectedPages);

      // Act & Assert
      await expect(
        service.validateAndConnectFacebookPages(agentId, userId, pageIds)
      ).rejects.toThrow(new AppException(AGENT_ERROR_CODES.FACEBOOK_PAGE_ALREADY_CONNECTED));

      expect(facebookPageRepository.findPagesNotOwnedByUser).toHaveBeenCalledWith(userId, pageIds);
      expect(facebookPageRepository.findPagesConnectedToOtherAgents).toHaveBeenCalledWith(pageIds, agentId);
      expect(facebookPageRepository.updateAgentIdForMultiplePages).not.toHaveBeenCalled();
    });

    it('should not execute batch update if validation fails', async () => {
      // Arrange
      facebookPageRepository.findPagesNotOwnedByUser.mockResolvedValue(['page-1']);

      // Act & Assert
      await expect(
        service.validateAndConnectFacebookPages(agentId, userId, pageIds)
      ).rejects.toThrow();

      // Verify batch update was not called
      expect(facebookPageRepository.updateAgentIdForMultiplePages).not.toHaveBeenCalled();
    });
  });

  describe('processOutputMessengerBlock', () => {
    const agentId = 'agent-123';
    const userId = 1;

    it('should process multiple Facebook page IDs using batch validation', async () => {
      // Arrange
      const outputMessenger = {
        facebookPageIds: ['page-1', 'page-2', 'page-3'],
      };

      const validateSpy = jest.spyOn(service, 'validateAndConnectFacebookPages').mockResolvedValue(undefined);

      // Act
      await service.processOutputMessengerBlock(agentId, userId, outputMessenger);

      // Assert
      expect(validateSpy).toHaveBeenCalledTimes(1);
      expect(validateSpy).toHaveBeenCalledWith(agentId, userId, ['page-1', 'page-2', 'page-3']);
    });

    it('should handle empty Facebook page IDs array', async () => {
      // Arrange
      const outputMessenger = {
        facebookPageIds: [],
      };

      const validateSpy = jest.spyOn(service, 'validateAndConnectFacebookPages');

      // Act
      await service.processOutputMessengerBlock(agentId, userId, outputMessenger);

      // Assert
      expect(validateSpy).not.toHaveBeenCalled();
    });

    it('should handle missing facebookPageIds property', async () => {
      // Arrange
      const outputMessenger = {};

      const validateSpy = jest.spyOn(service, 'validateAndConnectFacebookPages');

      // Act
      await service.processOutputMessengerBlock(agentId, userId, outputMessenger);

      // Assert
      expect(validateSpy).not.toHaveBeenCalled();
    });

    it('should handle null facebookPageIds', async () => {
      // Arrange
      const outputMessenger = {
        facebookPageIds: null,
      };

      const validateSpy = jest.spyOn(service, 'validateAndConnectFacebookPages');

      // Act
      await service.processOutputMessengerBlock(agentId, userId, outputMessenger);

      // Assert
      expect(validateSpy).not.toHaveBeenCalled();
    });
  });

  describe('Repository Integration Tests', () => {
    const agentId = 'agent-123';
    const userId = 1;
    const pageIds = ['page-1', 'page-2'];

    it('should call repository methods in correct order for validation', async () => {
      // Arrange
      facebookPageRepository.findPagesNotOwnedByUser.mockResolvedValue([]);
      facebookPageRepository.findPagesConnectedToOtherAgents.mockResolvedValue([]);
      facebookPageRepository.updateAgentIdForMultiplePages.mockResolvedValue(undefined);

      // Act
      await service.validateAndConnectFacebookPages(agentId, userId, pageIds);

      // Assert - Verify call order
      expect(facebookPageRepository.findPagesNotOwnedByUser).toHaveBeenCalledBefore(
        facebookPageRepository.findPagesConnectedToOtherAgents as jest.Mock
      );
      expect(facebookPageRepository.findPagesConnectedToOtherAgents).toHaveBeenCalledBefore(
        facebookPageRepository.updateAgentIdForMultiplePages as jest.Mock
      );
    });
  });
});
