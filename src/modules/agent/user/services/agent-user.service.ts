import {
  AgentMediaRepository,
  AgentProductRepository,
  AgentRepository,
  AgentUserRepository,
  AgentUserToolsRepository,
  TypeAgentRepository,
  UserMultiAgentRepository
} from '@modules/agent/repositories';
import { AgentUrlRepository } from '@modules/agent/repositories/agent-url.repository';

import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { Agent, AgentUser } from '@modules/agent/entities';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { TypeAgentConfig } from '@modules/agent/interfaces/type-agent-config.interface';
import { AgentListMapper } from '@modules/agent/mappers/agent-list.mapper';
import { AgentSimpleListDto, AgentSimpleQueryDto } from '@modules/agent/user/dto';
import { FacebookPageRepository, UserWebsiteRepository } from '@modules/integration/repositories';
import { ZaloOfficialAccountRepository } from '@modules/marketing/user/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';
import { FacebookService } from '@shared/services/facebook/facebook.service';
import { FileSizeEnum, ImageType, TimeIntervalEnum } from '@shared/utils';
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import { Transactional } from 'typeorm-transactional';
import { In } from 'typeorm';
import { AgentListItemDto, AgentQueryDto, CreateAgentResponseDto } from '../dto/agent';
import {
  CreateAgentDto,
  OutputWebsiteBlockDto,
  OutputZaloBlockDto,
  ResourcesBlockDto
} from '../dto/agent/create-agent.dto';
import { ProfileMapper } from '../mappers';

/**
 * Service xử lý các thao tác liên quan đến agent cho người dùng
 */
@Injectable()
export class AgentUserService {
  private readonly logger = new Logger(AgentUserService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly agentUserRepository: AgentUserRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly agentMediaRepository: AgentMediaRepository,
    private readonly agentProductRepository: AgentProductRepository,
    private readonly agentUrlRepository: AgentUrlRepository,
    private readonly userMultiAgentRepository: UserMultiAgentRepository,
    private readonly agentUserToolsRepository: AgentUserToolsRepository,
    private readonly facebookPageRepository: FacebookPageRepository,
    private readonly userWebsiteRepository: UserWebsiteRepository,
    private readonly zaloOfficialAccountRepository: ZaloOfficialAccountRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly agentListMapper: AgentListMapper,
  ) { }

  /**
   * Tạo agent mới với cấu trúc modular
   * @param userId ID của người dùng
   * @param createDto Thông tin agent mới theo cấu hình TypeAgent
   * @returns Thông tin tạo agent thành công
   */
  @Transactional()
  async createAgent(
    userId: number,
    createDto: CreateAgentDto,
  ): Promise<CreateAgentResponseDto> {
    try {
      this.logger.log(
        `Bắt đầu tạo agent cho user ${userId}: ${createDto.name}`,
      );

      // 1. Lấy TypeAgent và TypeAgentConfig từ typeId
      const typeAgent = await this.typeAgentRepository.findById(
        createDto.typeId,
      );
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      const typeAgentConfig = this.mapLegacyConfigToNewFormat(typeAgent.config); // TypeAgentConfig từ trường config
      this.logger.log(`TypeAgentConfig: ${JSON.stringify(typeAgentConfig)}`);

      // 3. Kiểm tra tên agent đã tồn tại cho user này chưa
      const existingAgent = await this.agentRepository.existsByNameAndUserId(
        createDto.name,
        userId,
      );

      if (existingAgent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NAME_EXISTS);
      }

      // 4. Tạo agent entity cơ bản
      const agent = await this.createAgentEntity(createDto, userId);

      // 5. Xử lý các blocks dữ liệu theo TypeAgentConfig
      await this.processAgentBlocks(agent, createDto, typeAgentConfig, userId);

      // 6. Tạo S3 key cho avatar nếu có
      let avatarUploadUrl: string | undefined;
      if (createDto.avatarMimeType && agent.avatar) {
        avatarUploadUrl = await this.s3Service.createPresignedWithID(
          agent.avatar,
          TimeIntervalEnum.ONE_HOUR,
          ImageType.getType(createDto.avatarMimeType),
          FileSizeEnum.FIVE_MB,
        );
      }

      this.logger.log(`Đã tạo agent thành công: ${agent.id}`);

      return {
        id: agent.id,
        avatarUploadUrl: avatarUploadUrl || null,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo agent: ${error.message}`, error.stack);
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        error.message,
      );
    }
  }

  /**
   * Map legacy config format to new TypeAgentConfig format
   * @param legacyConfig Config từ database có thể có format cũ
   * @returns TypeAgentConfig với format mới
   */
  private mapLegacyConfigToNewFormat(legacyConfig: TypeAgentConfig): TypeAgentConfig {

    return {
      enableAgentProfileCustomization: legacyConfig.enableAgentProfileCustomization || false,
      enableOutputToMessenger: legacyConfig.enableOutputToMessenger || false,
      enableOutputToWebsiteLiveChat: legacyConfig.enableOutputToWebsiteLiveChat || false,
      enableTaskConversionTracking: legacyConfig.enableTaskConversionTracking || false,
      enableResourceUsage: legacyConfig.enableResourceUsage || false,
      enableDynamicStrategyExecution: legacyConfig.enableDynamicStrategyExecution || false,
      enableMultiAgentCollaboration: legacyConfig.enableMultiAgentCollaboration || false,
      enableOutputToZaloOA: legacyConfig.enableOutputToZaloOA || false,
    };
  }

  /**
   * Tạo agent entity cơ bản
   * @param createDto DTO tạo agent
   * @param userId ID của user
   * @returns Agent entity đã được lưu
   */
  private async createAgentEntity(
    createDto: CreateAgentDto,
    userId: number,
  ): Promise<Agent> {
    try {
      // Tạo agent entity cơ bản
      const agent = new Agent();
      agent.name = createDto.name;
      agent.modelConfig = createDto.modelConfig;
      agent.instruction = createDto.instruction || null;
      agent.avatar = generateS3Key({
        baseFolder: userId.toString(),
        categoryFolder: CategoryFolderEnum.AGENT,
      });
      agent.vectorStoreId = createDto.vectorStoreId || null;

      // Lưu agent vào database
      const savedAgent = await this.agentRepository.save(agent);

      // Tạo agent user relationship
      const agentUser = new AgentUser();
      agentUser.id = savedAgent.id;
      agentUser.userId = userId;
      agentUser.typeId = createDto.typeId;
      agentUser.profile = createDto.profile
        ? ProfileMapper.fromDto(createDto.profile)
        : {};

      // Lưu convert config từ DTO - chuyển đổi ConversionBlockDto sang ConvertConfig
      agentUser.convertConfig = createDto.conversion
        ? createDto.conversion.map(item => ({
          name: item.name,
          type: item.type,
          description: item.description || '', // Đảm bảo description không undefined
          required: item.required || false,
          defaultValue: undefined // ConversionBlockDto không có defaultValue
        }))
        : [];

      // Lưu model fields từ createDto - sử dụng đúng tên trường trong entity
      agentUser.systemModelId = createDto.systemModelId || null;
      agentUser.keyLlmId = createDto.keyLlmId || null;
      agentUser.userModelId = createDto.userModelId || null;
      agentUser.modelFineTuneId = createDto.modelFineTuneId || null;

      // Lưu agent user relationship
      await this.agentUserRepository.save(agentUser);

      this.logger.log(
        `Đã tạo agent entity: ${savedAgent.id} cho user ${userId}`,
      );
      return savedAgent;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo agent entity: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý các khối cấu hình agent theo TypeAgent config
   * @param agent Agent entity
   * @param createDto Dữ liệu tạo agent
   * @param typeAgentConfig Cấu hình TypeAgent
   * @param userId ID của người dùng
   */
  private async processAgentBlocks(
    agent: Agent,
    createDto: CreateAgentDto,
    typeAgentConfig: TypeAgentConfig,
    userId: number,
  ): Promise<void> {
    try {
      // Xử lý các blocks theo config - chỉ xử lý khi có dữ liệu
      if (typeAgentConfig.enableResourceUsage && createDto.resources) {
        await this.processResourceBlock(agent.id, createDto.resources);
      }

      if (
        typeAgentConfig.enableDynamicStrategyExecution &&
        createDto.strategy
      ) {
        await this.processStrategyBlock(agent.id, userId, createDto.strategy);
      }

      if (
        typeAgentConfig.enableMultiAgentCollaboration &&
        createDto.multiAgent
      ) {
        await this.processMultiAgentBlock(
          agent.id,
          userId,
          createDto.multiAgent,
        );
      }

      if (
        typeAgentConfig.enableOutputToMessenger &&
        createDto.outputMessenger
      ) {
        await this.processOutputMessengerBlock(
          agent.id,
          userId,
          createDto.outputMessenger,
        );
      }

      if (
        typeAgentConfig.enableOutputToWebsiteLiveChat &&
        createDto.outputWebsite
      ) {
        await this.processOutputWebsiteBlock(
          agent.id,
          userId,
          createDto.outputWebsite,
        );
      }

      if (typeAgentConfig.enableOutputToZaloOA && createDto.outputZalo) {
        await this.processOutputZaloBlock(
          agent.id,
          userId,
          createDto.outputZalo,
        );
      }

      this.logger.log(`Đã xử lý các blocks cho agent: ${agent.id}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý agent blocks: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo URL upload avatar cho agent
   * @param agentId ID của agent
   * @param mimeType MIME type của avatar
   * @param userId ID của user
   * @returns URL upload avatar
   */
  private async generateAvatarUploadUrl(
    agentId: string,
    mimeType: string,
    userId: number,
  ): Promise<string> {
    try {
      // Tạo S3 key cho avatar
      const avatarKey = generateS3Key({
        baseFolder: userId.toString(),
        categoryFolder: CategoryFolderEnum.AGENT,
      });

      // Tạo presigned URL
      const uploadUrl = await this.s3Service.createPresignedWithID(
        avatarKey,
        TimeIntervalEnum.ONE_HOUR,
        ImageType.getType(mimeType),
        FileSizeEnum.FIVE_MB,
      );

      // Cập nhật avatar key cho agent
      await this.agentRepository.updateAvatar(agentId, avatarKey);

      this.logger.log(`Đã tạo avatar upload URL cho agent: ${agentId}`);
      return uploadUrl;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo avatar upload URL: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.INVALID_S3_KEY, error.message);
    }
  }

  /**
   * Xử lý Resource block - Lưu các tài nguyên vào database
   * @param agentId ID của agent
   * @param resources Dữ liệu resources từ DTO
   */
  private async processResourceBlock(
    agentId: string,
    resources: ResourcesBlockDto,
  ): Promise<void> {
    try {
      this.logger.log(`Processing resource block for agent ${agentId}`);

      // 1. Xử lý Media IDs
      if (resources.mediaIds && Array.isArray(resources.mediaIds)) {
        for (const mediaId of resources.mediaIds) {
          // Kiểm tra media đã được thêm vào agent chưa
          const existingAgentMedia = await this.agentMediaRepository.findOne({
            where: { agentId, mediaId },
          });

          if (!existingAgentMedia) {
            // Tạo và lưu agent media relationship
            const agentMedia = this.agentMediaRepository.create({
              agentId,
              mediaId,
            });
            await this.agentMediaRepository.save(agentMedia);
            this.logger.debug(`Added media ${mediaId} to agent ${agentId}`);
          }
        }
      }

      // 2. Xử lý Product IDs
      if (resources.productIds && Array.isArray(resources.productIds)) {
        for (const productId of resources.productIds) {
          // Kiểm tra product đã được thêm vào agent chưa
          const existingAgentProduct =
            await this.agentProductRepository.findOne({
              where: { agentId, productId: productId },
            });

          if (!existingAgentProduct) {
            // Tạo và lưu agent product relationship
            const agentProduct = this.agentProductRepository.create({
              agentId,
              productId: productId,
            });
            await this.agentProductRepository.save(agentProduct);
            this.logger.debug(`Added product ${productId} to agent ${agentId}`);
          }
        }
      }

      // 3. Xử lý URL IDs
      if (resources.urlIds && Array.isArray(resources.urlIds)) {
        for (const urlId of resources.urlIds) {
          // Kiểm tra URL đã được thêm vào agent chưa
          const existingAgentUrl = await this.agentUrlRepository.findOne({
            where: { agentId, urlId },
          });

          if (!existingAgentUrl) {
            // Tạo và lưu agent URL relationship
            const agentUrl = this.agentUrlRepository.create({
              agentId,
              urlId,
            });
            await this.agentUrlRepository.save(agentUrl);
            this.logger.debug(`Added URL ${urlId} to agent ${agentId}`);
          }
        }
      }

      this.logger.log(
        `Successfully processed resource block for agent ${agentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing resource block for agent ${agentId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
        error.message,
      );
    }
  }

  /**
   * Xử lý Strategy block - Cập nhật strategyId trong AgentUser
   * @param agentId ID của agent
   * @param userId ID của user
   * @param strategy Dữ liệu strategy từ DTO
   */
  private async processStrategyBlock(
    agentId: string,
    userId: number,
    strategy: any,
  ): Promise<void> {
    try {
      this.logger.log(`Processing strategy block for agent ${agentId}`);

      // Xử lý Strategy ID
      if (strategy.strategyId) {
        // Cập nhật strategyId trong AgentUser
        await this.agentUserRepository.updateStrategyId(
          agentId,
          userId,
          strategy.strategyId,
        );
        this.logger.debug(
          `Updated strategy ${strategy.strategyId} for agent ${agentId}`,
        );
      }

      this.logger.log(
        `Successfully processed strategy block for agent ${agentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing strategy block for agent ${agentId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_STRATEGY_NOT_SUPPORTED,
        error.message,
      );
    }
  }

  /**
   * Xử lý Multi Agent block - Lưu quan hệ hợp tác multi-agent
   * @param agentId ID của agent
   * @param userId ID của user
   * @param multiAgent Dữ liệu multi agent từ DTO
   */
  private async processMultiAgentBlock(
    agentId: string,
    userId: number,
    multiAgent: any,
  ): Promise<void> {
    try {
      this.logger.log(`Processing multi agent block for agent ${agentId}`);

      // Xử lý multi agent items
      if (multiAgent.multiAgent && Array.isArray(multiAgent.multiAgent)) {
        if (multiAgent.multiAgent.length > 0) {
          // Sử dụng repository method để bulk add collaboration agents
          const agentIds = multiAgent.multiAgent.map((item) => item.agent_id);
          const result =
            await this.userMultiAgentRepository.bulkAddCollaborationAgents(
              agentId,
              agentIds,
              undefined, // Có thể thêm prompt từ DTO nếu cần
            );

          this.logger.debug(
            `Bulk add collaboration agents for agent ${agentId}: ` +
            `${result.addedCount} added, ${result.skippedCount} skipped (already exist)`,
          );

          if (result.existingIds.length > 0) {
            this.logger.debug(
              `Existing collaboration agents: [${result.existingIds.join(', ')}]`,
            );
          }
        }
      }

      this.logger.log(
        `Successfully processed multi agent block for agent ${agentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing multi agent block for agent ${agentId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_MULTI_AGENT_NOT_SUPPORTED,
        error.message,
      );
    }
  }

  /**
   * Xử lý Output Messenger block - Lưu quan hệ với Facebook Pages
   * @param agentId ID của agent
   * @param userId ID của user
   * @param outputMessenger Dữ liệu output messenger từ DTO
   */
  private async processOutputMessengerBlock(
    agentId: string,
    userId: number,
    outputMessenger: any,
  ): Promise<void> {
    try {
      this.logger.log(`Processing output messenger block for agent ${agentId}`);

      // Xử lý Facebook Page IDs
      if (
        outputMessenger.facebookPageIds &&
        Array.isArray(outputMessenger.facebookPageIds) &&
        outputMessenger.facebookPageIds.length > 0
      ) {
        await this.validateAndConnectFacebookPages(
          agentId,
          userId,
          outputMessenger.facebookPageIds
        );
      }

      this.logger.log(
        `Successfully processed output messenger block for agent ${agentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing output messenger block for agent ${agentId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.FACEBOOK_PAGE_CONNECTION_FAILED,
        error.message,
      );
    }
  }

  /**
   * Validate và kết nối nhiều Facebook Pages với Agent (batch processing)
   * @param agentId ID của agent
   * @param userId ID của user
   * @param pageIds Danh sách ID của Facebook Pages
   */
  private async validateAndConnectFacebookPages(
    agentId: string,
    userId: number,
    pageIds: string[],
  ): Promise<void> {
    try {
      this.logger.log(
        `Validating and connecting ${pageIds.length} Facebook pages to agent ${agentId}`
      );

      // Phase 1: Validation - Kiểm tra tất cả pages trước
      await this.validateFacebookPagesOwnership(userId, pageIds);
      await this.validateFacebookPagesConnection(pageIds, agentId);

      // Phase 2: Execution - Chỉ thực hiện khi tất cả pages hợp lệ
      await this.facebookPageRepository.updateAgentIdForMultiplePages(pageIds, agentId);

      this.logger.log(
        `Successfully connected ${pageIds.length} Facebook pages to agent ${agentId}`
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error validating and connecting Facebook pages to agent ${agentId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.FACEBOOK_PAGE_CONNECTION_FAILED,
        error.message,
      );
    }
  }

  /**
   * Kiểm tra quyền sở hữu của Facebook Pages
   * @param userId ID của user
   * @param pageIds Danh sách ID của Facebook Pages
   */
  private async validateFacebookPagesOwnership(
    userId: number,
    pageIds: string[],
  ): Promise<void> {
    const notOwnedPageIds = await this.facebookPageRepository.findPagesNotOwnedByUser(
      userId,
      pageIds
    );

    if (notOwnedPageIds.length > 0) {
      this.logger.warn(
        `Facebook pages not owned by user ${userId}: ${notOwnedPageIds.join(', ')}`
      );
      throw new AppException(
        AGENT_ERROR_CODES.FACEBOOK_PAGE_NOT_OWNED,
        `Các Facebook Page sau không thuộc về bạn: ${notOwnedPageIds.join(', ')}`
      );
    }

    this.logger.log(`All ${pageIds.length} Facebook pages are owned by user ${userId}`);
  }

  /**
   * Kiểm tra trạng thái kết nối của Facebook Pages
   * @param pageIds Danh sách ID của Facebook Pages
   * @param excludeAgentId ID của agent cần loại trừ
   */
  private async validateFacebookPagesConnection(
    pageIds: string[],
    excludeAgentId: string,
  ): Promise<void> {
    const connectedPages = await this.facebookPageRepository.findPagesConnectedToOtherAgents(
      pageIds,
      excludeAgentId
    );

    if (connectedPages.length > 0) {
      const conflictInfo = connectedPages
        .map(p => `Page ${p.pageId} đã kết nối với Agent ${p.agentId}`)
        .join(', ');

      this.logger.warn(
        `Facebook pages already connected to other agents: ${conflictInfo}`
      );
      throw new AppException(
        AGENT_ERROR_CODES.FACEBOOK_PAGE_ALREADY_CONNECTED,
        `Các Facebook Page sau đã được kết nối với agent khác: ${conflictInfo}`
      );
    }

    this.logger.log(`All ${pageIds.length} Facebook pages are available for connection`);
  }

  /**
   * Xử lý Output Website block - Lưu quan hệ với User Websites
   * @param agentId ID của agent
   * @param userId ID của user
   * @param outputWebsite Dữ liệu output website từ DTO
   */
  private async processOutputWebsiteBlock(
    agentId: string,
    userId: number,
    outputWebsite: OutputWebsiteBlockDto,
  ): Promise<void> {
    try {
      this.logger.log(`Processing output website block for agent ${agentId}`);

      // Xử lý User Website IDs
      if (
        outputWebsite.userWebsiteIds &&
        Array.isArray(outputWebsite.userWebsiteIds) &&
        outputWebsite.userWebsiteIds.length > 0
      ) {
        // Lấy tất cả websites trong một truy vấn duy nhất
        const userWebsites = await this.userWebsiteRepository.findByIdsAndUserId(
          outputWebsite.userWebsiteIds,
          userId,
        );

        if (userWebsites.length === 0) {
          this.logger.warn(
            `No valid websites found for user ${userId} in provided IDs`,
          );
          return;
        }

        // Tạo map để tra cứu nhanh
        const websiteMap = new Map(userWebsites.map(w => [w.id, w]));

        // Kiểm tra và validate từng website
        const validWebsiteIds: string[] = [];

        for (const websiteId of outputWebsite.userWebsiteIds) {
          const userWebsite = websiteMap.get(websiteId);

          if (!userWebsite) {
            this.logger.error(
              `Website ${websiteId} not found or not owned by user ${userId}`
            );
            throw new AppException(
              AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
              `Website ${websiteId} không tồn tại hoặc không thuộc về user`
            );
          }

          // Kiểm tra Website đã được gán cho agent khác chưa
          if (userWebsite.agentId && userWebsite.agentId !== agentId) {
            this.logger.error(
              `Website ${websiteId} is already connected to agent ${userWebsite.agentId}`
            );
            throw new AppException(
              AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
              `Website ${websiteId} đã được kết nối với agent khác`
            );
          }

          validWebsiteIds.push(websiteId);
        }

        // Cập nhật tất cả website hợp lệ trong một truy vấn batch
        if (validWebsiteIds.length > 0) {
          const updateResult = await this.userWebsiteRepository
            .createQueryBuilder()
            .update()
            .set({ agentId })
            .where('id IN (:...websiteIds)', { websiteIds: validWebsiteIds })
            .andWhere('userId = :userId', { userId })
            .execute();

          if (updateResult.affected === 0) {
            this.logger.error(`Failed to update websites with agent ${agentId}`);
            throw new AppException(
              AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
              `Không thể gán agent vào các websites`
            );
          }

          this.logger.log(
            `Successfully connected ${updateResult.affected} websites to agent ${agentId}`,
          );
        }
      }

      this.logger.log(
        `Successfully processed output website block for agent ${agentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing output website block for agent ${agentId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
        error.message,
      );
    }
  }

  private async processOutputZaloBlock(
    agentId: string,
    userId: number,
    outputZalo: OutputZaloBlockDto,
  ): Promise<void> {
    try {
      this.logger.log(`Processing output zalo block for agent ${agentId}`);

      // Xử lý Zalo Official Account IDs
      if (outputZalo.zaloOfficialAccountIds && Array.isArray(outputZalo.zaloOfficialAccountIds) && outputZalo.zaloOfficialAccountIds.length > 0) {

        // Lấy tất cả Zalo OAs trong một truy vấn duy nhất
        const zaloOAs = await this.zaloOfficialAccountRepository.find({
          where: {
            id: In(outputZalo.zaloOfficialAccountIds),
            userId: userId,
            status: 'active' // Chỉ cho phép kết nối với OA đang hoạt động
          }
        });

        if (zaloOAs.length === 0) {
          this.logger.error(
            `No valid Zalo Official Accounts found for user ${userId} in provided IDs`
          );
          throw new AppException(
            AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
            `Không tìm thấy Zalo Official Account hợp lệ nào`
          );
        }

        // Tạo map để tra cứu nhanh
        const zaloOAMap = new Map(zaloOAs.map(oa => [oa.id, oa]));

        // Kiểm tra và validate từng OA
        for (const oaId of outputZalo.zaloOfficialAccountIds) {
          const zaloOA = zaloOAMap.get(oaId);

          if (!zaloOA) {
            this.logger.error(
              `Zalo Official Account ${oaId} not found or not owned by user ${userId}`
            );
            throw new AppException(
              AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
              `Zalo Official Account ${oaId} không tồn tại hoặc không thuộc về user`
            );
          }

          // Kiểm tra OA đã được gán cho agent khác chưa
          if (zaloOA.agentId && zaloOA.agentId !== agentId) {
            this.logger.error(
              `Zalo Official Account ${oaId} is already connected to agent ${zaloOA.agentId}`
            );
            throw new AppException(
              AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
              `Zalo Official Account ${oaId} đã được kết nối với agent khác`
            );
          }
        }

        // Gán agent ID vào tất cả Zalo Official Accounts hợp lệ
        await this.zaloOfficialAccountRepository.updateAgentId(
          agentId,
          outputZalo.zaloOfficialAccountIds
        );
      }
    } catch (error) {
      this.logger.error(
        `Error processing output zalo block for agent ${agentId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
        error.message,
      );
    }
  }

  /**
   * Lấy danh sách agent đơn giản của người dùng (chỉ id, avatar, name)
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn với phân trang
   * @returns Danh sách agent đơn giản có phân trang
   */
  async getSimpleAgentList(
    userId: number,
    queryDto: AgentSimpleQueryDto,
  ): Promise<PaginatedResult<AgentSimpleListDto>> {
    try {
      // Lấy danh sách agent từ repository với phân trang
      const result = await this.agentRepository.findSimpleListByUserIdPaginated(
        userId,
        queryDto,
      );

      // Chuyển đổi avatar thành URL CDN
      const items = result.items.map((item) => ({
        id: item.id,
        name: item.name,
        avatar: item.avatar
          ? this.cdnService.generateUrlView(
            item.avatar,
            TimeIntervalEnum.ONE_DAY,
          ) || undefined
          : undefined,
      }));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách agent đơn giản: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_LIST_QUERY_FAILED,
        'Không thể lấy danh sách agent',
      );
    }
  }

  /**
   * Lấy danh sách agent của người dùng
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent có phân trang
   */
  async getAgents(
    userId: number,
    queryDto: AgentQueryDto,
  ): Promise<PaginatedResult<AgentListItemDto>> {
    try {
      // Gọi repository để lấy dữ liệu raw với phân trang
      const result = await this.agentUserRepository.getAgentsList(
        userId,
        queryDto,
      );

      // Chuyển đổi raw data sang DTO bằng mapper
      const dtoItems = this.agentListMapper.toDtoList(result.items);

      return {
        items: dtoItems,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_LIST_QUERY_FAILED,
        'Không thể lấy danh sách agent',
      );
    }
  }

  /**
   * Xóa agent (soft delete) và gỡ bỏ tất cả tài nguyên liên quan
   * @param agentId ID của agent
   * @param userId ID của người dùng
   */
  @Transactional()
  async deleteAgent(agentId: string, userId: number): Promise<void> {
    try {
      // Kiểm tra agent có tồn tại và thuộc về user không
      const agentExists = await this.agentUserRepository.findAgentByIdAndUserId(
        agentId,
        userId,
      );

      if (!agentExists) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_NOT_FOUND,
          'Agent không tồn tại hoặc không thuộc về bạn',
        );
      }

      // 1. Gỡ bỏ Facebook Pages khỏi agent (set agentId = null)
      await this.removeFacebookPagesFromAgent(agentId, userId);

      // 2. Gỡ bỏ Websites khỏi agent (set agentId = null)
      await this.removeWebsitesFromAgent(agentId, userId);

      // 3. Gỡ bỏ tất cả Tools khỏi agent
      await this.removeAllToolsFromAgent(agentId);

      // 4. Gỡ bỏ tất cả Zalo Official Accounts khỏi agent
      await this.removeZaloOfficialAccountsFromAgent(agentId, userId);

      // 5. Thực hiện soft delete trên bảng agents
      const deleteResult = await this.agentRepository
        .createQueryBuilder()
        .update()
        .set({
          deletedAt: Date.now(),
        })
        .where('id = :agentId', { agentId })
        .execute();

      if (deleteResult.affected === 0) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_DELETE_FAILED,
          'Không thể xóa agent',
        );
      }

      this.logger.log(`Agent ${agentId} đã được xóa và gỡ bỏ tất cả tài nguyên bởi user ${userId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi xóa agent ${agentId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_DELETE_FAILED,
        'Không thể xóa agent',
      );
    }
  }

  /**
   * Đảo ngược trạng thái hoạt động của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Trạng thái mới của agent
   */
  @Transactional()
  async updateAgentActive(
    agentId: string,
    userId: number,
  ): Promise<{ active: boolean }> {
    try {
      // Kiểm tra agent có tồn tại và thuộc về user không
      const agentData = await this.agentUserRepository.findAgentByIdAndUserId(
        agentId,
        userId,
      );

      if (!agentData) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_NOT_FOUND,
          'Agent không tồn tại hoặc không thuộc về bạn',
        );
      }

      // Đảo ngược trạng thái active
      const newActiveStatus = !agentData.agentUser.active;

      // Cập nhật trạng thái trong bảng agents_user
      const updateResult = await this.agentUserRepository
        .createQueryBuilder()
        .update()
        .set({ active: newActiveStatus })
        .where('id = :agentId', { agentId })
        .andWhere('userId = :userId', { userId })
        .execute();

      if (updateResult.affected === 0) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
          'Không thể cập nhật trạng thái agent',
        );
      }

      this.logger.log(
        `Agent ${agentId} active status changed to ${newActiveStatus} by user ${userId}`,
      );

      return { active: newActiveStatus };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật trạng thái agent ${agentId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
        'Không thể cập nhật trạng thái agent',
      );
    }
  }

  /**
   * Gỡ bỏ tất cả Facebook Pages khỏi agent khi xóa agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   */
  private async removeFacebookPagesFromAgent(agentId: string, userId: number): Promise<void> {
    try {
      // Tìm tất cả Facebook Pages đang được tích hợp với agent này
      // Sử dụng JOIN với facebook_personal để lọc theo userId
      const facebookPages = await this.facebookPageRepository
        .createQueryBuilder('page')
        .innerJoin('facebook_personal', 'personal', 'page.facebook_personal_id = personal.id')
        .where('page.agent_id = :agentId', { agentId })
        .andWhere('personal.user_id = :userId', { userId })
        .andWhere('page.deleted_at IS NULL')
        .getMany();

      if (facebookPages.length > 0) {
        // Gỡ bỏ agentId khỏi tất cả Facebook Pages
        // Sử dụng subquery để update với điều kiện userId
        await this.facebookPageRepository
          .createQueryBuilder()
          .update()
          .set({
            agentId: null,
            isActive: false
          })
          .where('agent_id = :agentId', { agentId })
          .andWhere(`facebook_personal_id IN (
            SELECT id FROM facebook_personal WHERE user_id = :userId
          )`, { userId })
          .execute();

        this.logger.log(`Đã gỡ bỏ ${facebookPages.length} Facebook Pages khỏi agent ${agentId}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ bỏ Facebook Pages khỏi agent ${agentId}: ${error.message}`, error.stack);
      // Không throw error để không làm gián đoạn quá trình xóa agent
    }
  }

  /**
   * Gỡ bỏ tất cả Websites khỏi agent khi xóa agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   */
  private async removeWebsitesFromAgent(agentId: string, userId: number): Promise<void> {
    try {
      // Tìm tất cả Websites đang được tích hợp với agent này
      // Sử dụng đúng tên field trong database
      const websites = await this.userWebsiteRepository
        .createQueryBuilder('website')
        .where('website.agent_id = :agentId', { agentId })
        .andWhere('website.user_id = :userId', { userId })
        .getMany();

      if (websites.length > 0) {
        // Gỡ bỏ agentId khỏi tất cả Websites
        await this.userWebsiteRepository
          .createQueryBuilder()
          .update()
          .set({
            agentId: null
          })
          .where('agent_id = :agentId', { agentId })
          .andWhere('user_id = :userId', { userId })
          .execute();

        this.logger.log(`Đã gỡ bỏ ${websites.length} Websites khỏi agent ${agentId}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ bỏ Websites khỏi agent ${agentId}: ${error.message}`, error.stack);
      // Không throw error để không làm gián đoạn quá trình xóa agent
    }
  }

  /**
   * Gỡ bỏ tất cả Tools khỏi agent khi xóa agent
   * @param agentId ID của agent
   */
  private async removeAllToolsFromAgent(agentId: string): Promise<void> {
    try {
      // Xóa tất cả relationships trong bảng agent_user_tools
      // Sử dụng đúng tên field trong database
      const deleteResult = await this.agentUserToolsRepository
        .createQueryBuilder()
        .delete()
        .where('user_agent_id = :agentId', { agentId })
        .execute();

      const deletedCount = deleteResult.affected || 0;
      if (deletedCount > 0) {
        this.logger.log(`Đã gỡ bỏ ${deletedCount} Tools khỏi agent ${agentId}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ bỏ Tools khỏi agent ${agentId}: ${error.message}`, error.stack);
      // Không throw error để không làm gián đoạn quá trình xóa agent
    }
  }

  /**
   * Gỡ bỏ tất cả Zalo Official Accounts khỏi agent khi xóa agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   */
  private async removeZaloOfficialAccountsFromAgent(agentId: string, userId: number): Promise<void> {
    try {
      // Sử dụng method có sẵn trong repository để gỡ tất cả OAs
      const removedCount = await this.zaloOfficialAccountRepository.removeAllFromAgent(agentId, userId);

      if (removedCount > 0) {
        this.logger.log(`Đã gỡ bỏ ${removedCount} Zalo Official Accounts khỏi agent ${agentId}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ bỏ Zalo OAs khỏi agent ${agentId}: ${error.message}`, error.stack);
      // Không throw error để không làm gián đoạn quá trình xóa agent
    }
  }
}
